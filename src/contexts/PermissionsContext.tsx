import React, { createContext, useContext, useEffect, useState, useCallback, useMemo } from 'react';
import { useAuth } from './AuthContext';
import { useWorkspace } from './WorkspaceContext';
import {
  PermissionsContextType,
  UserRole,
  Permission,
  PermissionError,
  PermissionErrorCode,
  SubscriptionPlan,
  WorkspaceStatus,
} from '@/types';

const PermissionsContext = createContext<PermissionsContextType | undefined>(undefined);

export const usePermissions = () => {
  const context = useContext(PermissionsContext);
  if (context === undefined) {
    throw new Error('usePermissions must be used within a PermissionsProvider');
  }
  return context;
};

interface PermissionsProviderProps {
  children: React.ReactNode;
}

// Role hierarchy mapping (higher number = more permissions)
const ROLE_HIERARCHY: Record<UserRole, number> = {
  [UserRole.VIEWER]: 1,
  [UserRole.MEMBER]: 2,
  [UserRole.ADMIN]: 3,
  [UserRole.OWNER]: 4,
};

// Permission mappings for each role - built hierarchically to avoid circular references
const VIEWER_PERMISSIONS: Permission[] = [
  Permission.VIEW_LISTINGS,
  Permission.VIEW_INTERNAL_NOTES,
  Permission.VIEW_ACTIVITY_FEED,
];

const MEMBER_PERMISSIONS: Permission[] = [
  ...VIEWER_PERMISSIONS,
  Permission.CREATE_LISTINGS,
  Permission.EDIT_LISTINGS,
  Permission.DELETE_LISTINGS,
  Permission.ADD_INTERNAL_NOTES,
  Permission.MENTION_TEAM_MEMBERS,
];

const ADMIN_PERMISSIONS: Permission[] = [
  ...MEMBER_PERMISSIONS,
  Permission.VIEW_WORKSPACE_SETTINGS,
  Permission.MANAGE_TEAM,
  Permission.INVITE_MEMBERS,
  Permission.REMOVE_MEMBERS,
  Permission.ASSIGN_ROLES,
  Permission.ASSIGN_LISTINGS,
];

const OWNER_PERMISSIONS: Permission[] = [
  ...ADMIN_PERMISSIONS,
  Permission.MANAGE_WORKSPACE,
  Permission.MANAGE_BILLING,
];

const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  [UserRole.VIEWER]: VIEWER_PERMISSIONS,
  [UserRole.MEMBER]: MEMBER_PERMISSIONS,
  [UserRole.ADMIN]: ADMIN_PERMISSIONS,
  [UserRole.OWNER]: OWNER_PERMISSIONS,
};

// Feature access based on subscription plans
const PLAN_FEATURES: Record<SubscriptionPlan, string[]> = {
  [SubscriptionPlan.TRIAL]: [
    'basic_listings',
    'team_collaboration',
    'internal_notes',
  ],
  [SubscriptionPlan.BASIC]: [
    'basic_listings',
    'team_collaboration',
    'internal_notes',
    'advanced_search',
    'export_data',
  ],
  [SubscriptionPlan.PRO]: [
    'basic_listings',
    'team_collaboration',
    'internal_notes',
    'advanced_search',
    'export_data',
    'custom_branding',
    'advanced_analytics',
    'api_access',
  ],
  [SubscriptionPlan.ENTERPRISE]: [
    'basic_listings',
    'team_collaboration',
    'internal_notes',
    'advanced_search',
    'export_data',
    'custom_branding',
    'advanced_analytics',
    'api_access',
    'white_label',
    'priority_support',
    'custom_integrations',
  ],
};

export const PermissionsProvider: React.FC<PermissionsProviderProps> = ({ children }) => {
  const { profile, workspace } = useAuth();
  const { teamMembers } = useWorkspace();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<PermissionError | null>(null);

  // Helper function to create PermissionError
  const createPermissionError = useCallback((
    message: string,
    code: PermissionErrorCode,
    requiredPermission?: Permission,
    requiredRole?: UserRole
  ): PermissionError => {
    return {
      code,
      message,
      required_permission: requiredPermission,
      required_role: requiredRole,
      current_role: profile?.role || null,
      timestamp: new Date().toISOString(),
    };
  }, [profile?.role]);

  // Get user role
  const userRole = useMemo(() => profile?.role || null, [profile?.role]);

  // Get user permissions based on role
  const permissions = useMemo(() => {
    if (!userRole) return [];
    return ROLE_PERMISSIONS[userRole] || [];
  }, [userRole]);

  // Check if user has specific permission
  const hasPermission = useCallback((permission: Permission): boolean => {
    if (!userRole || !workspace) return false;

    // Check workspace status
    if (workspace.status === WorkspaceStatus.SUSPENDED) {
      return false;
    }

    // Check if workspace trial has expired
    if (workspace.status === WorkspaceStatus.TRIAL && workspace.trial_ends_at) {
      const trialEndsAt = new Date(workspace.trial_ends_at);
      const now = new Date();
      if (now > trialEndsAt) {
        return false;
      }
    }

    return permissions.includes(permission);
  }, [userRole, workspace, permissions]);

  // Check if user can access a feature based on subscription plan
  const canAccessFeature = useCallback((feature: string): boolean => {
    if (!workspace) return false;

    // Check workspace status
    if (workspace.status === WorkspaceStatus.SUSPENDED) {
      return false;
    }

    // Check if workspace trial has expired
    if (workspace.status === WorkspaceStatus.TRIAL && workspace.trial_ends_at) {
      const trialEndsAt = new Date(workspace.trial_ends_at);
      const now = new Date();
      if (now > trialEndsAt) {
        return false;
      }
    }

    const planFeatures = PLAN_FEATURES[workspace.subscription_plan] || [];
    return planFeatures.includes(feature);
  }, [workspace]);

  // Check if user can manage team
  const canManageTeam = useCallback((): boolean => {
    return hasPermission(Permission.MANAGE_TEAM);
  }, [hasPermission]);

  // Check if user can manage billing
  const canManageBilling = useCallback((): boolean => {
    return hasPermission(Permission.MANAGE_BILLING);
  }, [hasPermission]);

  // Check if user can edit a specific listing
  const canEditListing = useCallback((listingId: string): boolean => {
    if (!hasPermission(Permission.EDIT_LISTINGS)) {
      return false;
    }

    // Additional logic could be added here to check listing ownership
    // For now, if user has edit permission, they can edit any listing in their workspace
    return true;
  }, [hasPermission]);

  // Check if user can view workspace settings
  const canViewWorkspaceSettings = useCallback((): boolean => {
    return hasPermission(Permission.VIEW_WORKSPACE_SETTINGS);
  }, [hasPermission]);

  // Check if user can invite members
  const canInviteMembers = useCallback((): boolean => {
    return hasPermission(Permission.INVITE_MEMBERS);
  }, [hasPermission]);

  // Check if user can remove members
  const canRemoveMembers = useCallback((): boolean => {
    return hasPermission(Permission.REMOVE_MEMBERS);
  }, [hasPermission]);

  // Check if user can assign roles
  const canAssignRoles = useCallback((): boolean => {
    return hasPermission(Permission.ASSIGN_ROLES);
  }, [hasPermission]);

  // Monitor workspace and profile changes for permission updates
  useEffect(() => {
    if (!profile || !workspace) {
      setError(null);
      return;
    }

    // Clear any existing errors when context changes
    setError(null);

    // Check for permission-related issues
    if (workspace.status === WorkspaceStatus.SUSPENDED) {
      setError(createPermissionError(
        'Workspace is suspended. Contact support for assistance.',
        PermissionErrorCode.FEATURE_RESTRICTED
      ));
    } else if (workspace.status === WorkspaceStatus.TRIAL && workspace.trial_ends_at) {
      const trialEndsAt = new Date(workspace.trial_ends_at);
      const now = new Date();
      if (now > trialEndsAt) {
        setError(createPermissionError(
          'Trial period has expired. Upgrade your subscription to continue.',
          PermissionErrorCode.FEATURE_RESTRICTED
        ));
      }
    }
  }, [profile, workspace, createPermissionError]);

  const value: PermissionsContextType = {
    userRole,
    permissions,
    hasPermission,
    canAccessFeature,
    canManageTeam,
    canManageBilling,
    canEditListing,
    canViewWorkspaceSettings,
    canInviteMembers,
    canRemoveMembers,
    canAssignRoles,
    loading,
    error,
  };

  return (
    <PermissionsContext.Provider value={value}>
      {children}
    </PermissionsContext.Provider>
  );
};