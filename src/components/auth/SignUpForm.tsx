import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Progress } from '@/components/ui/progress';
import { Textarea } from '@/components/ui/textarea';
import { SimplePasswordField } from '@/components/ui/password-field';
import { ArrowLeft, ArrowRight, Building, User, Mail, Shield } from 'lucide-react';
import { SignUpData, CompanyType } from '@/types';
import { z } from 'zod';
import { Spinner } from '@/components/ui/spinner';

// Zod validation schemas for each step
const personalInfoSchema = z.object({
  first_name: z.string().min(1, 'First name is required').max(50, 'First name too long'),
  last_name: z.string().min(1, 'Last name is required').max(50, 'Last name too long'),
  email: z.string().email('Invalid email address'),
  phone: z.string().optional(),
  license_number: z.string().optional(),
});

const companyInfoSchema = z.object({
  company_name: z.string().min(1, 'Company name is required').max(100, 'Company name too long'),
  company_type: z.nativeEnum(CompanyType),
  website: z.string().url('Invalid website URL').optional().or(z.literal('')),
  address: z.string().optional(),
});

const securitySchema = z.object({
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain uppercase, lowercase, and number'),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

const termsSchema = z.object({
  terms_accepted: z.boolean().refine(val => val === true, 'You must accept the terms and conditions'),
  marketing_consent: z.boolean().optional(),
});

// Combined schema for final validation (simplified to avoid ZodEffects issue)
const fullSignUpSchema = personalInfoSchema
  .merge(companyInfoSchema)
  .merge(z.object({
    password: z.string().min(8, 'Password must be at least 8 characters'),
    confirmPassword: z.string(),
    terms_accepted: z.boolean(),
    marketing_consent: z.boolean().optional(),
  }));

interface SignUpFormProps {
  onSwitchToSignIn?: () => void;
}

enum SignUpStep {
  PERSONAL_INFO = 1,
  COMPANY_INFO = 2,
  SECURITY = 3,
  TERMS = 4,
}

const STEP_TITLES = {
  [SignUpStep.PERSONAL_INFO]: 'Personal Information',
  [SignUpStep.COMPANY_INFO]: 'Company Details',
  [SignUpStep.SECURITY]: 'Account Security',
  [SignUpStep.TERMS]: 'Terms & Preferences',
};

const STEP_DESCRIPTIONS = {
  [SignUpStep.PERSONAL_INFO]: 'Tell us about yourself',
  [SignUpStep.COMPANY_INFO]: 'Set up your workspace',
  [SignUpStep.SECURITY]: 'Secure your account',
  [SignUpStep.TERMS]: 'Review and accept terms',
};

const STEP_ICONS = {
  [SignUpStep.PERSONAL_INFO]: User,
  [SignUpStep.COMPANY_INFO]: Building,
  [SignUpStep.SECURITY]: Shield,
  [SignUpStep.TERMS]: Mail,
};

export const SignUpForm: React.FC<SignUpFormProps> = ({ onSwitchToSignIn }) => {
  const { signUp, loading, error, clearError } = useAuth();
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState<SignUpStep>(SignUpStep.PERSONAL_INFO);
  const [formData, setFormData] = useState<SignUpData>({
    email: '',
    password: '',
    confirmPassword: '',
    first_name: '',
    last_name: '',
    company_name: '',
    company_type: CompanyType.TEAM,
    phone: '',
    license_number: '',
    website: '',
    address: '',
    terms_accepted: false,
    marketing_consent: false,
  });

  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [emailVerificationSent, setEmailVerificationSent] = useState(false);

  const validateCurrentStep = (): boolean => {
    const errors: Record<string, string> = {};
    
    try {
      switch (currentStep) {
        case SignUpStep.PERSONAL_INFO:
          personalInfoSchema.parse({
            first_name: formData.first_name,
            last_name: formData.last_name,
            email: formData.email,
            phone: formData.phone,
            license_number: formData.license_number,
          });
          break;
        case SignUpStep.COMPANY_INFO:
          companyInfoSchema.parse({
            company_name: formData.company_name,
            company_type: formData.company_type,
            website: formData.website,
            address: formData.address,
          });
          break;
        case SignUpStep.SECURITY:
          securitySchema.parse({
            password: formData.password,
            confirmPassword: formData.confirmPassword,
          });
          break;
        case SignUpStep.TERMS:
          termsSchema.parse({
            terms_accepted: formData.terms_accepted,
            marketing_consent: formData.marketing_consent,
          });
          break;
      }
    } catch (error) {
      if (error instanceof z.ZodError) {
        error.errors.forEach((err) => {
          if (err.path.length > 0) {
            errors[err.path[0] as string] = err.message;
          }
        });
      }
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const validateFullForm = (): boolean => {
    const errors: Record<string, string> = {};
    
    try {
      fullSignUpSchema.parse(formData);
    } catch (error) {
      if (error instanceof z.ZodError) {
        error.errors.forEach((err) => {
          if (err.path.length > 0) {
            errors[err.path[0] as string] = err.message;
          }
        });
      }
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleNext = () => {
    if (validateCurrentStep()) {
      if (currentStep < SignUpStep.TERMS) {
        setCurrentStep(currentStep + 1);
        clearError();
      }
    }
  };

  const handlePrevious = () => {
    if (currentStep > SignUpStep.PERSONAL_INFO) {
      setCurrentStep(currentStep - 1);
      clearError();
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateFullForm()) {
      return;
    }

    try {
      await signUp(formData);
      // setEmailVerificationSent(true);
      clearError();
      // On successful signup, go straight to dashboard
      navigate('/dashboard', { replace: true });
    } catch (error) {
      // Error is handled by the AuthContext
      console.error('Sign up failed:', error);
    }
  };

  const handleInputChange = (field: keyof SignUpData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear validation error when user starts typing
    if (validationErrors[field]) {
      setValidationErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  // Show email verification message after successful signup
  if (emailVerificationSent) {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardHeader>
          <CardTitle className="text-center">Check Your Email</CardTitle>
          <CardDescription className="text-center">
            We've sent a verification link to {formData.email}
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center space-y-4">
          <div className="p-4 bg-green-50 rounded-lg">
            <Mail className="h-12 w-12 text-green-600 mx-auto mb-2" />
            <p className="text-sm text-green-800">
              Please check your email and click the verification link to activate your workspace.
            </p>
          </div>
          
          <div className="text-sm text-muted-foreground">
            <p>Didn't receive the email? Check your spam folder or</p>
            <Button
              variant="link"
              className="p-0 h-auto text-sm"
              onClick={() => setEmailVerificationSent(false)}
            >
              try signing up again
            </Button>
          </div>
          
          {onSwitchToSignIn && (
            <Button
              variant="outline"
              onClick={onSwitchToSignIn}
              className="w-full"
            >
              Back to Sign In
            </Button>
          )}
        </CardContent>
      </Card>
    );
  }

  const StepIcon = STEP_ICONS[currentStep];
  const progress = (currentStep / Object.keys(SignUpStep).length * 2) * 100;

  return (
    <Card className="w-full max-w-lg mx-auto">
      <CardHeader>
        <div className="flex items-center space-x-2 mb-2">
          <StepIcon className="h-5 w-5 text-primary" />
          <CardTitle>{STEP_TITLES[currentStep]}</CardTitle>
        </div>
        <CardDescription>
          {STEP_DESCRIPTIONS[currentStep]}
        </CardDescription>
        <div className="space-y-2">
          <div className="flex justify-between text-sm text-muted-foreground">
            <span>Step {currentStep} of {Object.keys(SignUpStep).length / 2}</span>
            <span>{Math.round(progress)}% complete</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>
      </CardHeader>
      <CardContent>
        <form onSubmit={currentStep === SignUpStep.TERMS ? handleSubmit : (e) => { e.preventDefault(); handleNext(); }} className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>
                {error.message}
              </AlertDescription>
            </Alert>
          )}
          
          {currentStep === SignUpStep.PERSONAL_INFO && (
            <>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="first_name">First Name</Label>
                  <Input
                    id="first_name"
                    value={formData.first_name}
                    onChange={(e) => handleInputChange('first_name', e.target.value)}
                    placeholder="John"
                    disabled={loading}
                  />
                  {validationErrors.first_name && (
                    <p className="text-sm text-destructive">{validationErrors.first_name}</p>
                  )}
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="last_name">Last Name</Label>
                  <Input
                    id="last_name"
                    value={formData.last_name}
                    onChange={(e) => handleInputChange('last_name', e.target.value)}
                    placeholder="Doe"
                    disabled={loading}
                  />
                  {validationErrors.last_name && (
                    <p className="text-sm text-destructive">{validationErrors.last_name}</p>
                  )}
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="email">Email Address</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  placeholder="<EMAIL>"
                  disabled={loading}
                />
                {validationErrors.email && (
                  <p className="text-sm text-destructive">{validationErrors.email}</p>
                )}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="phone">Phone Number (Optional)</Label>
                <Input
                  id="phone"
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  placeholder="+****************"
                  disabled={loading}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="license_number">License Number (Optional)</Label>
                <Input
                  id="license_number"
                  value={formData.license_number}
                  onChange={(e) => handleInputChange('license_number', e.target.value)}
                  placeholder="License #"
                  disabled={loading}
                />
              </div>
            </>
          )}

          {currentStep === SignUpStep.COMPANY_INFO && (
            <>
              <div className="space-y-2">
                <Label htmlFor="company_name">Company Name</Label>
                <Input
                  id="company_name"
                  value={formData.company_name}
                  onChange={(e) => handleInputChange('company_name', e.target.value)}
                  placeholder="Your Company LLC"
                  disabled={loading}
                />
                {validationErrors.company_name && (
                  <p className="text-sm text-destructive">{validationErrors.company_name}</p>
                )}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="company_type">Company Type</Label>
                <Select 
                  value={formData.company_type} 
                  onValueChange={(value) => handleInputChange('company_type', value as CompanyType)}
                  disabled={loading}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select company type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={CompanyType.INDIVIDUAL}>Individual Broker</SelectItem>
                    <SelectItem value={CompanyType.TEAM}>Team/Partnership</SelectItem>
                    <SelectItem value={CompanyType.FIRM}>Brokerage Firm</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="website">Website (Optional)</Label>
                <Input
                  id="website"
                  type="url"
                  value={formData.website}
                  onChange={(e) => handleInputChange('website', e.target.value)}
                  placeholder="https://yourcompany.com"
                  disabled={loading}
                />
                {validationErrors.website && (
                  <p className="text-sm text-destructive">{validationErrors.website}</p>
                )}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="address">Business Address (Optional)</Label>
                <Textarea
                  id="address"
                  value={formData.address}
                  onChange={(e) => handleInputChange('address', e.target.value)}
                  placeholder="123 Main St, City, State 12345"
                  disabled={loading}
                  rows={3}
                />
              </div>
            </>
          )}

          {currentStep === SignUpStep.SECURITY && (
            <>
              <div className="space-y-2">
                <SimplePasswordField
                  id="password"
                  label="Password"
                  value={formData.password}
                  onChange={(e) => handleInputChange('password', e.target.value)}
                  placeholder="Enter a strong password"
                  disabled={loading}
                />
                {validationErrors.password && (
                  <p className="text-sm text-destructive">{validationErrors.password}</p>
                )}
                <p className="text-xs text-muted-foreground">
                  Password must be at least 8 characters with uppercase, lowercase, and number
                </p>
              </div>
              
              <div className="space-y-2">
                <SimplePasswordField
                  id="confirmPassword"
                  label="Confirm Password"
                  value={formData.confirmPassword}
                  onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                  placeholder="Confirm your password"
                  disabled={loading}
                />
                {validationErrors.confirmPassword && (
                  <p className="text-sm text-destructive">{validationErrors.confirmPassword}</p>
                )}
              </div>
            </>
          )}

          {currentStep === SignUpStep.TERMS && (
            <>
              <div className="space-y-4">
                <div className="p-4 bg-muted rounded-lg">
                  <h4 className="font-medium mb-2">Review Your Information</h4>
                  <div className="text-sm space-y-1">
                    <p><strong>Name:</strong> {formData.first_name} {formData.last_name}</p>
                    <p><strong>Email:</strong> {formData.email}</p>
                    <p><strong>Company:</strong> {formData.company_name}</p>
                    <p><strong>Type:</strong> {formData.company_type}</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-2">
                  <Checkbox
                    id="terms_accepted"
                    checked={formData.terms_accepted}
                    onCheckedChange={(checked) => handleInputChange('terms_accepted', !!checked)}
                    disabled={loading}
                  />
                  <Label htmlFor="terms_accepted" className="text-sm leading-relaxed">
                    I accept the <Button variant="link" className="p-0 h-auto text-sm">Terms of Service</Button> and <Button variant="link" className="p-0 h-auto text-sm">Privacy Policy</Button>
                  </Label>
                </div>
                {validationErrors.terms_accepted && (
                  <p className="text-sm text-destructive">{validationErrors.terms_accepted}</p>
                )}
                
                <div className="flex items-start space-x-2">
                  <Checkbox
                    id="marketing_consent"
                    checked={formData.marketing_consent}
                    onCheckedChange={(checked) => handleInputChange('marketing_consent', !!checked)}
                    disabled={loading}
                  />
                  <Label htmlFor="marketing_consent" className="text-sm leading-relaxed">
                    I agree to receive marketing communications and product updates
                  </Label>
                </div>
              </div>
            </>
          )}
          
          <div className="flex justify-between pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handlePrevious}
              disabled={currentStep === SignUpStep.PERSONAL_INFO || loading}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Previous
            </Button>
            
            {currentStep === SignUpStep.TERMS ? (
              <Button 
                type="submit" 
                disabled={loading || !formData.terms_accepted}
              >
                {loading ? (
                  <>
                    <Spinner size="sm" className="mr-2" />
                    Creating Workspace...
                  </>
                ) : (
                  'Create Workspace'
                )}
              </Button>
            ) : (
              <Button 
                type="submit"
                disabled={loading}
              >
                Next
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            )}
          </div>
          
          {onSwitchToSignIn && (
            <div className="text-center pt-4 border-t">
              <Button
                type="button"
                variant="link"
                onClick={onSwitchToSignIn}
                disabled={loading}
                className="text-sm"
              >
                Already have an account? Sign in
              </Button>
            </div>
          )}
        </form>
      </CardContent>
    </Card>
  );
};