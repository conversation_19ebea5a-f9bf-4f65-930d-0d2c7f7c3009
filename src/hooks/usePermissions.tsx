import { usePermissions as usePermissionsContext } from '@/contexts/PermissionsContext';
import { Permission, UserRole } from '@/types';

/**
 * Custom hook for permission checking with additional utilities
 */
export const usePermissions = () => {
  const context = usePermissionsContext();

  /**
   * Check if user has any of the specified permissions
   */
  const hasAnyPermission = (permissions: Permission[]): boolean => {
    return permissions.some(permission => context.hasPermission(permission));
  };

  /**
   * Check if user has all of the specified permissions
   */
  const hasAllPermissions = (permissions: Permission[]): boolean => {
    return permissions.every(permission => context.hasPermission(permission));
  };

  /**
   * Check if user has a role at or above the specified level
   */
  const hasRoleOrAbove = (role: UserRole): boolean => {
    if (!context.userRole) return false;

    const roleHierarchy: Record<UserRole, number> = {
      [UserRole.VIEWER]: 1,
      [UserRole.MEMBER]: 2,
      [UserRole.ADMIN]: 3,
      [UserRole.OWNER]: 4,
    };

    const userLevel = roleHierarchy[context.userRole];
    const requiredLevel = roleHierarchy[role];

    return userLevel >= requiredLevel;
  };

  /**
   * Check if user has exactly the specified role
   */
  const hasExactRole = (role: UserRole): boolean => {
    return context.userRole === role;
  };

  /**
   * Check if user is workspace owner
   */
  const isOwner = (): boolean => {
    return context.userRole === UserRole.OWNER;
  };

  /**
   * Check if user is admin or owner
   */
  const isAdminOrOwner = (): boolean => {
    return hasRoleOrAbove(UserRole.ADMIN);
  };

  /**
   * Check if user is broker or above
   */
  const isMemberOrAbove = (): boolean => {
    return hasRoleOrAbove(UserRole.MEMBER);
  };

  /**
   * Check if user can perform listing operations
   */
  const canManageListings = (): boolean => {
    return hasAnyPermission([
      Permission.CREATE_LISTINGS,
      Permission.EDIT_LISTINGS,
      Permission.DELETE_LISTINGS,
    ]);
  };

  /**
   * Check if user can perform team operations
   */
  const canManageTeamMembers = (): boolean => {
    return hasAnyPermission([
      Permission.INVITE_MEMBERS,
      Permission.REMOVE_MEMBERS,
      Permission.ASSIGN_ROLES,
    ]);
  };

  /**
   * Get user-friendly role display name
   */
  const getRoleDisplayName = (role?: UserRole): string => {
    if (!role) return 'Unknown';

    const roleNames: Record<UserRole, string> = {
      [UserRole.OWNER]: 'Owner',
      [UserRole.ADMIN]: 'Administrator',
      [UserRole.MEMBER]: 'Member',
      [UserRole.VIEWER]: 'Viewer',
    };

    return roleNames[role];
  };

  /**
   * Get permissions display names
   */
  const getPermissionDisplayName = (permission: Permission): string => {
    const permissionNames: Record<Permission, string> = {
      [Permission.MANAGE_WORKSPACE]: 'Manage Workspace',
      [Permission.VIEW_WORKSPACE_SETTINGS]: 'View Workspace Settings',
      [Permission.MANAGE_BILLING]: 'Manage Billing',
      [Permission.MANAGE_TEAM]: 'Manage Team',
      [Permission.INVITE_MEMBERS]: 'Invite Members',
      [Permission.REMOVE_MEMBERS]: 'Remove Members',
      [Permission.ASSIGN_ROLES]: 'Assign Roles',
      [Permission.CREATE_LISTINGS]: 'Create Listings',
      [Permission.EDIT_LISTINGS]: 'Edit Listings',
      [Permission.DELETE_LISTINGS]: 'Delete Listings',
      [Permission.VIEW_LISTINGS]: 'View Listings',
      [Permission.ASSIGN_LISTINGS]: 'Assign Listings',
      [Permission.ADD_INTERNAL_NOTES]: 'Add Internal Notes',
      [Permission.VIEW_INTERNAL_NOTES]: 'View Internal Notes',
      [Permission.MENTION_TEAM_MEMBERS]: 'Mention Team Members',
      [Permission.VIEW_ACTIVITY_FEED]: 'View Activity Feed',
    };

    return permissionNames[permission] || permission;
  };

  return {
    ...context,
    hasAnyPermission,
    hasAllPermissions,
    hasRoleOrAbove,
    hasExactRole,
    isOwner,
    isAdminOrOwner,
    isMemberOrAbove,
    canManageListings,
    canManageTeamMembers,
    getRoleDisplayName,
    getPermissionDisplayName,
  };
};