import { useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/hooks/use-toast';
import { AuthErrorCode } from '@/types';

export const useTokenExpiration = () => {
  const navigate = useNavigate();
  const { toast, dismiss } = useToast();

  const handleTokenExpiration = useCallback((error?: any, redirectTo: string = '/') => {
    // Clear any pending toasts
    dismiss();
    navigate(redirectTo, { replace: true });
   
  }, [navigate, toast, dismiss]);

  const handleUnauthorizedAccess = useCallback((message?: string, redirectTo: string = '/unauthorized') => {
    // Clear any pending toasts
    dismiss();

    // Show unauthorized access toast
    toast({
      title: "Access Denied",
      description: message || "You don't have permission to access this resource.",
      variant: "destructive",
      duration: 3000, // 3 seconds
    });

    // Navigate to unauthorized page after a brief delay to ensure toast is visible
    setTimeout(() => {
      navigate(redirectTo, { replace: true });
    }, 100);
  }, [navigate, toast, dismiss]);

  const isTokenExpiredError = useCallback((error: any): boolean => {
    // Check if it's a session expired error
    if (error?.code === AuthErrorCode.SESSION_EXPIRED) {
      return true;
    }

    // Check if it's a 401 error from API
    if (error?.status === 401) {
      return true;
    }

    // Check for common token expiration messages
    if (typeof error === 'string' || error?.message) {
      const message = (error?.message || error).toLowerCase();
      return message.includes('session expired') || 
             message.includes('token expired') ||
             message.includes('unauthorized') ||
             message.includes('invalid token');
    }

    return false;
  }, []);

  return {
    handleTokenExpiration,
    handleUnauthorizedAccess,
    isTokenExpiredError,
  };
}; 