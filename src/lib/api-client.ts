import { API_CONFIG, API_ENDPOINTS } from "./api-config";
import { createAuthClient } from "better-auth/react";
import { magicLinkClient } from "better-auth/client/plugins";
import type { components } from "@/types/api";
import type {
  Workspace as AppWorkspace,
  UserProfile as AppUserProfile,
  AuthSession as AppAuthSession,
  SignUpData,
  CompanyType,
} from "@/types";

// Session response type for the /get-session endpoint
export interface SessionResponse {
  session: {
    id: string;
    userId: string;
    activeOrganizationId?: string | null;
    expiresAt: string;
    createdAt: string;
    updatedAt: string;
  };
  user: {
    id: string;
    email: string;
    name?: string;
    emailVerified: boolean;
    image?: string;
    createdAt: string;
    updatedAt: string;
  };
}

// App-level types (from our domain models)
export interface User {
  id: string;
  email?: string;
  [key: string]: unknown;
}
export type Workspace = AppWorkspace;
export type UserProfile = AppUserProfile;
export type AuthSession = AppAuthSession;

// Profile types (from API schemas)
export type UpdateProfileRequest =
  components["schemas"]["UpdateProfileRequest"];
export type UpdateProfileResponse =
  components["schemas"]["UpdateProfileResponse"];

// File types
export type UploadedFile = components["schemas"]["File"];
export type UploadFileRequest = components["schemas"]["UploadFileRequest"];
export type UploadFileResponse = components["schemas"]["UploadFileResponse"];
export type GetFileResponse = components["schemas"]["GetFileResponse"];
export type DeleteResponse = components["schemas"]["DeleteResponse"];

// Listing types
export type ListingResponse = components["schemas"]["ListingResponse"];
export type ListingListResponse = components["schemas"]["ListingListResponse"];
export type SingleListingResponse =
  components["schemas"]["SingleListingResponse"];
export type CreateListingRequest =
  components["schemas"]["CreateListingRequest"];
export type UpdateListingRequest =
  components["schemas"]["UpdateListingRequest"];
export type SaveDraftListingRequest =
  components["schemas"]["SaveDraftListingRequest"];
export type BulkCreateResponse = components["schemas"]["BulkCreateResponse"];

// Workspace types (mapped to app domain models or lightweight placeholders)
export type SingleWorkspaceResponse = Workspace;
export type WorkspaceUpdateResponse = Workspace;
export type UpdateWorkspaceRequest = Partial<Workspace>;
export type WorkspaceInvitation = import("@/types").WorkspaceInvitation;
export type WorkspaceInvitationListResponse = { data: WorkspaceInvitation[] };
export type SingleWorkspaceInvitationResponse = { data: WorkspaceInvitation };
export type CreateWorkspaceInvitationRequest = {
  email: string;
  role: string;
  redirectTo?: string;
};
export type UpdateWorkspaceInvitationRequest = {
  role?: string;
  status?: string;
};

// Convenient type alias
export type Listing = ListingResponse;

// Legacy types for backward compatibility (remove once all usage is updated)
export interface UserWorkspacesResponse {
  workspaces: {
    workspace_id: string;
    workspace_name: string;
    company_name: string;
    user_role: string;
    is_active: boolean;
    status: string;
    subscription_plan: string;
    logo_url?: string;
    primary_color?: string;
  }[];
}

export interface SwitchWorkspaceRequest {
  workspace_id: string;
}

export interface SwitchWorkspaceResponse {
  success: boolean;
  workspace: {
    id: string;
    companyName: string;
    companyType: string;
    subscriptionPlan: string;
    status: string;
    createdAt: string;
  };
  profile: {
    id: string;
    workspaceId: string;
    role: string;
    firstName: string;
    lastName: string;
    displayName: string;
    email: string;
  };
  session: {
    access_token: string;
    refresh_token: string;
    expires_at: number;
  };
}

export interface ApiError {
  message: string;
  status?: number;
  code?: string;
}



// Lightweight API client used by hooks and components
class ApiClient {
  private readonly baseUrl: string;

  constructor(baseUrl: string = API_CONFIG.BASE_URL) {
    this.baseUrl = baseUrl;
  }

  private async request<T>(path: string, init: RequestInit = {}): Promise<T> {
    const response = await fetch(`${this.baseUrl}${path}`, {
      credentials: "include",
      headers: {
        "Content-Type": "application/json",
        ...(init.headers || {}),
      },
      ...init,
    });

    const isJson = response.headers.get("content-type")?.includes("application/json");
    const data = isJson ? await response.json() : (await response.text());

    if (!response.ok) {
      const message = (data as any)?.message || response.statusText || "Request failed";
      const error: ApiError = { message, status: response.status };
      throw error;
    }

    return data as T;
  }

  private toQuery(params?: Record<string, any>): string {
    if (!params) return "";
    const search = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value === undefined || value === null || value === "") return;
      search.append(key, String(value));
    });
    const query = search.toString();
    return query ? `?${query}` : "";
  }

  // Auth
  async getSession(): Promise<SessionResponse> {
    // Endpoint aligned with SessionResponse description
    return this.request<SessionResponse>(`/get-session`, { method: "GET" });
  }

  async signInWithEmail(data: { email: string; password: string; rememberMe?: boolean }): Promise<any> {
    return this.request(API_ENDPOINTS.AUTH.SIGNIN, {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  async signUpWithEmail(data: SignUpData): Promise<any> {
    return this.request(API_ENDPOINTS.AUTH.SIGNUP, {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  async signOut(_data?: Record<string, unknown>): Promise<any> {
    return this.request(API_ENDPOINTS.AUTH.SIGNOUT, { method: "POST" });
  }

  // Listings
  async getListings(params?: Record<string, any>): Promise<ListingListResponse> {
    const query = this.toQuery(params);
    return this.request<ListingListResponse>(`${API_ENDPOINTS.LISTINGS.LIST}${query}`, { method: "GET" });
  }

  async getListing(listingId: string, includeDetails = true): Promise<SingleListingResponse> {
    const path = API_ENDPOINTS.LISTINGS.GET.replace(":id", listingId);
    const query = this.toQuery({ includeDetails });
    return this.request<SingleListingResponse>(`${path}${query}`, { method: "GET" });
  }

  async createListing(listingData: CreateListingRequest): Promise<ListingResponse> {
    return this.request<ListingResponse>(API_ENDPOINTS.LISTINGS.CREATE, {
      method: "POST",
      body: JSON.stringify(listingData),
    });
  }

  async updateListing(listingId: string, listingData: UpdateListingRequest): Promise<ListingResponse> {
    const path = API_ENDPOINTS.LISTINGS.UPDATE.replace(":id", listingId);
    return this.request<ListingResponse>(path, {
      method: "PUT",
      body: JSON.stringify(listingData),
    });
  }

  async deleteListing(listingId: string): Promise<DeleteResponse> {
    const path = API_ENDPOINTS.LISTINGS.DELETE.replace(":id", listingId);
    return this.request<DeleteResponse>(path, { method: "DELETE" });
  }

  async updateListingStatus(listingId: string, statusUpdate: { status: string; reason?: string; notes?: string }): Promise<ListingResponse> {
    // Fallback to update route if dedicated status route not available
    const path = API_ENDPOINTS.LISTINGS.UPDATE.replace(":id", listingId);
    return this.request<ListingResponse>(path, {
      method: "PATCH",
      body: JSON.stringify(statusUpdate),
    });
  }

  async saveDraftListing(listingData: SaveDraftListingRequest): Promise<ListingResponse> {
    // Use create endpoint as draft handling is server-side
    return this.request<ListingResponse>(API_ENDPOINTS.LISTINGS.CREATE, {
      method: "POST",
      body: JSON.stringify(listingData),
    });
  }

  // User profile
  async getUserProfile(): Promise<UserProfile> {
    return this.request<UserProfile>(API_ENDPOINTS.USERS.PROFILE, { method: "GET" });
  }

  async updateUserProfile(profileData: UpdateProfileRequest): Promise<UpdateProfileResponse> {
    return this.request<UpdateProfileResponse>(API_ENDPOINTS.USERS.PROFILE, {
      method: "PUT",
      body: JSON.stringify(profileData),
    });
  }

  // Files
  async uploadFile(file: File, options: { fileType: "document" | "image" | "video" | "audio" | "other"; entityType?: string; entityId?: string; isPublic?: boolean }): Promise<UploadFileResponse> {
    const form = new FormData();
    form.append("file", file);
    form.append("fileType", options.fileType);
    if (options.entityType) form.append("entityType", options.entityType);
    if (options.entityId) form.append("entityId", options.entityId);
    if (typeof options.isPublic === "boolean") form.append("isPublic", String(options.isPublic));

    const response = await fetch(`${this.baseUrl}${API_ENDPOINTS.FILES.UPLOAD}`, {
      method: "POST",
      body: form,
      credentials: "include",
    });

    const data = await response.json();
    if (!response.ok) {
      const error: ApiError = { message: data?.message || response.statusText, status: response.status };
      throw error;
    }
    return data as UploadFileResponse;
  }

  async getFile(fileId: string): Promise<GetFileResponse> {
    const path = API_ENDPOINTS.FILES.GET.replace(":id", fileId);
    return this.request<GetFileResponse>(path, { method: "GET" });
  }

  async deleteFile(fileId: string): Promise<DeleteResponse> {
    const path = API_ENDPOINTS.FILES.DELETE.replace(":id", fileId);
    return this.request<DeleteResponse>(path, { method: "DELETE" });
  }

  async importListingsFromCSV(file: File): Promise<BulkCreateResponse> {
    const form = new FormData();
    form.append("file", file);

    const response = await fetch(`${this.baseUrl}${API_ENDPOINTS.LISTINGS.BULK_CSV}`, {
      method: "POST",
      body: form,
      credentials: "include",
    });

    const data = await response.json();
    if (!response.ok) {
      const error: ApiError = { message: data?.message || response.statusText, status: response.status };
      throw error;
    }
    return data as BulkCreateResponse;
  }

  // Generic helper
  async makeAuthenticatedRequest<T>(url: string, requestOptions: RequestInit = {}): Promise<T> {
    const response = await fetch(url.startsWith("http") ? url : `${this.baseUrl}${url}`, {
      credentials: "include",
      ...requestOptions,
    });
    const data = await response.json();
    if (!response.ok) {
      const error: ApiError = { message: data?.message || response.statusText, status: response.status };
      throw error;
    }
    return data as T;
  }
}

// Better Auth client configuration
export const authClient = createAuthClient({
  baseURL: import.meta.env.VITE_API_URL || "http://localhost:3001",
  plugins: [
    magicLinkClient(), // Enable magic link authentication
  ],
  fetchOptions: {
    onError: (ctx) => {
      // Global error handling
      console.error("Auth error:", ctx.error);
    },
    onSuccess: (ctx) => {
      // Global success handling
      console.log("Auth success:", ctx.response);
    },
  },
});

// Export Better Auth hooks and methods
export const {
  useSession,
  signIn,
  signUp,
  signOut,
  magicLink, // Magic link verification methods
} = authClient;

// Export error codes for custom error handling
export const AUTH_ERROR_CODES = authClient.$ERROR_CODES;

// Export singleton instance
export const apiClient = new ApiClient();

// Helper function to check if error is an API error
export const isApiError = (error: unknown): error is ApiError => {
  return (
    typeof error === "object" &&
    error !== null &&
    "message" in error &&
    typeof (error as any).message === "string"
  );
};
